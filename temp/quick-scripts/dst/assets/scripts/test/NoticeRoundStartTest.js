
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/test/NoticeRoundStartTest.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a1b2cPU5fZ4kKvN7xI0VniQ', 'NoticeRoundStartTest');
// scripts/test/NoticeRoundStartTest.ts

"use strict";
// 测试NoticeRoundStart消息处理的脚本
// 这个脚本可以用来模拟发送NoticeRoundStart消息，测试前端计时器更新功能
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameMgr_1 = require("../common/GameMgr");
var EventCenter_1 = require("../common/EventCenter");
var MessageId_1 = require("../net/MessageId");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var NoticeRoundStartTest = /** @class */ (function (_super) {
    __extends(NoticeRoundStartTest, _super);
    function NoticeRoundStartTest() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.testButton = null;
        _this.firstChoiceTestButton = null;
        _this.testGameStartAnimationBtn = null;
        _this.testRoundStartAnimationBtn = null;
        _this.testActionDisplayFlowBtn = null;
        _this.testCreateNodeBtn = null;
        _this.statusLabel = null;
        return _this;
    }
    NoticeRoundStartTest.prototype.start = function () {
        if (this.testButton) {
            this.testButton.node.on('click', this.sendTestMessage, this);
        }
        if (this.firstChoiceTestButton) {
            this.firstChoiceTestButton.node.on('click', this.testFirstChoiceBonusFlow, this);
        }
        if (this.testGameStartAnimationBtn) {
            this.testGameStartAnimationBtn.node.on('click', this.testGameStartAnimation, this);
        }
        if (this.testRoundStartAnimationBtn) {
            this.testRoundStartAnimationBtn.node.on('click', this.testRoundStartAnimation, this);
        }
        if (this.testActionDisplayFlowBtn) {
            this.testActionDisplayFlowBtn.node.on('click', this.testActionDisplayWithRoundStart, this);
        }
        if (this.testCreateNodeBtn) {
            this.testCreateNodeBtn.node.on('click', this.testCreateRoundStartNode, this);
        }
        if (this.statusLabel) {
            this.statusLabel.string = '点击按钮测试消息';
        }
    };
    // 发送测试的NoticeRoundStart消息
    NoticeRoundStartTest.prototype.sendTestMessage = function () {
        // 创建测试数据
        var testData = {
            roundNumber: 1,
            countDown: 25,
            gameStatus: 0
        };
        // 模拟接收到的消息格式
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeRoundStart,
            code: 0,
            msg: "success",
            data: testData
        };
        // 发送消息事件
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001\u6D4B\u8BD5\u6D88\u606F: \u56DE\u5408" + testData.roundNumber + ", \u5012\u8BA1\u65F6" + testData.countDown + "\u79D2";
        }
    };
    // 发送倒计时更新测试
    NoticeRoundStartTest.prototype.sendCountdownUpdate = function (seconds) {
        var testData = {
            roundNumber: 1,
            countDown: seconds,
            gameStatus: 0
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeRoundStart,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5012\u8BA1\u65F6\u66F4\u65B0: " + seconds + "\u79D2";
        }
    };
    // 测试不同的倒计时值
    NoticeRoundStartTest.prototype.testDifferentCountdowns = function () {
        var _this = this;
        // 测试25秒倒计时
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(25);
        }, 1);
        // 测试20秒倒计时（进入展示阶段）
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(20);
        }, 3);
        // 测试5秒倒计时（回合结束前）
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(5);
        }, 5);
        // 测试0秒倒计时（回合结束）
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(0);
        }, 7);
    };
    // 发送NoticeActionDisplay测试消息
    NoticeRoundStartTest.prototype.sendActionDisplayMessage = function () {
        var testData = {
            roundNumber: 1,
            gameStatus: 0,
            countDown: 5,
            playerActions: [
                {
                    userId: "player_001",
                    x: 3,
                    y: 2,
                    action: 1,
                    score: 1,
                    isFirstChoice: true,
                    result: 2 // 数字2
                },
                {
                    userId: "player_002",
                    x: 1,
                    y: 4,
                    action: 2,
                    score: 1,
                    isFirstChoice: false,
                    result: "correct_mark"
                }
            ],
            playerTotalScores: {
                "player_001": 5,
                "player_002": 3
            },
            remainingMines: 10,
            message: "展示阶段：显示所有玩家操作"
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeActionDisplay,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001ActionDisplay\u6D88\u606F: \u5C55\u793A\u9636\u6BB5\uFF0C\u5269\u4F59" + testData.countDown + "\u79D2\uFF0C\u5269\u4F59\u70B8\u5F39" + testData.remainingMines + "\u4E2A";
        }
    };
    // 测试完整的回合流程
    NoticeRoundStartTest.prototype.testFullRoundFlow = function () {
        var _this = this;
        // 1. 发送回合开始
        this.sendTestMessage();
        // 2. 20秒后发送操作展示
        this.scheduleOnce(function () {
            _this.sendActionDisplayMessage();
        }, 2);
        if (this.statusLabel) {
            this.statusLabel.string = '开始测试完整回合流程...';
        }
    };
    // 测试先手奖励和后续加分流程
    NoticeRoundStartTest.prototype.testFirstChoiceBonusFlow = function () {
        var _this = this;
        // 1. 发送回合开始
        this.sendTestMessage();
        // 2. 2秒后发送先手奖励
        this.scheduleOnce(function () {
            _this.sendFirstChoiceBonusMessage();
        }, 2);
        // 3. 4秒后发送操作展示（包含先手玩家）
        this.scheduleOnce(function () {
            _this.sendActionDisplayWithFirstChoiceMessage();
        }, 4);
        if (this.statusLabel) {
            this.statusLabel.string = '测试先手奖励+本回合加分...';
        }
    };
    // 发送NoticeFirstChoiceBonus测试消息
    NoticeRoundStartTest.prototype.sendFirstChoiceBonusMessage = function () {
        var testData = {
            userId: "player_001",
            roundNumber: 1,
            bonusScore: 1,
            totalScore: 6 // 原来5分 + 1分先手奖励
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeFirstChoiceBonus,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001FirstChoiceBonus: player_001\u83B7\u5F97+1\u5148\u624B\u5956\u52B1";
        }
    };
    // 发送包含先手玩家的NoticeActionDisplay测试消息
    NoticeRoundStartTest.prototype.sendActionDisplayWithFirstChoiceMessage = function () {
        var testData = {
            roundNumber: 1,
            gameStatus: 0,
            countDown: 5,
            playerActions: [
                {
                    userId: "player_001",
                    x: 3,
                    y: 2,
                    action: 1,
                    score: 2,
                    isFirstChoice: true,
                    result: 3 // 数字3
                },
                {
                    userId: "player_002",
                    x: 1,
                    y: 4,
                    action: 2,
                    score: 1,
                    isFirstChoice: false,
                    result: "correct_mark"
                }
            ],
            playerTotalScores: {
                "player_001": 8,
                "player_002": 4 // 原来3分 + 本回合1分
            },
            remainingMines: 9,
            message: "展示阶段：先手玩家应该显示两次player_game_pfb分数变化"
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeActionDisplay,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001ActionDisplay: \u5148\u624B\u73A9\u5BB6\u5E94\u663E\u793A+2\u5206";
        }
    };
    /**
     * 测试游戏开始动画
     */
    NoticeRoundStartTest.prototype.testGameStartAnimation = function () {
        var _this = this;
        if (this.statusLabel) {
            this.statusLabel.string = "测试游戏开始动画...";
        }
        // 获取GamePageController实例
        var gamePageController = window.gamePageController;
        if (gamePageController) {
            // 调用游戏开始动画
            if (gamePageController.showGameStartAnimation) {
                gamePageController.showGameStartAnimation();
                if (this.statusLabel) {
                    this.statusLabel.string = "游戏开始动画已触发";
                }
                // 3秒后隐藏
                this.scheduleOnce(function () {
                    if (gamePageController.hideGameStartAnimation) {
                        gamePageController.hideGameStartAnimation();
                        if (_this.statusLabel) {
                            _this.statusLabel.string = "游戏开始动画已隐藏";
                        }
                    }
                }, 3);
            }
            else {
                if (this.statusLabel) {
                    this.statusLabel.string = "GamePageController中没有找到showGameStartAnimation方法";
                }
            }
        }
        else {
            if (this.statusLabel) {
                this.statusLabel.string = "未找到GamePageController实例";
            }
        }
    };
    /**
     * 测试回合开始动画
     */
    NoticeRoundStartTest.prototype.testRoundStartAnimation = function () {
        if (this.statusLabel) {
            this.statusLabel.string = "测试回合开始动画...";
        }
        // 获取GamePageController实例
        var gamePageController = window.gamePageController;
        if (gamePageController) {
            // 直接调用回合开始动画
            if (gamePageController.showRoundStartAnimation) {
                console.log("🧪 测试：直接调用回合开始动画");
                gamePageController.showRoundStartAnimation();
                if (this.statusLabel) {
                    this.statusLabel.string = "回合开始动画已触发";
                }
            }
            else {
                if (this.statusLabel) {
                    this.statusLabel.string = "GamePageController中没有找到showRoundStartAnimation方法";
                }
            }
        }
        else {
            if (this.statusLabel) {
                this.statusLabel.string = "未找到GamePageController实例";
            }
        }
    };
    /**
     * 测试完整的ActionDisplay流程（包含回合开始动画）
     */
    NoticeRoundStartTest.prototype.testActionDisplayWithRoundStart = function () {
        if (this.statusLabel) {
            this.statusLabel.string = "测试ActionDisplay流程...";
        }
        // 创建测试数据
        var testData = {
            roundNumber: 1,
            gameStatus: 0,
            countDown: 5,
            playerActions: [],
            playerTotalScores: {},
            remainingMines: 10,
            message: "测试消息"
        };
        // 模拟接收到的消息格式
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeActionDisplay,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "已发送ActionDisplay消息，等待回合开始动画...";
        }
    };
    /**
     * 简单测试：直接创建和显示回合开始节点
     */
    NoticeRoundStartTest.prototype.testCreateRoundStartNode = function () {
        if (this.statusLabel) {
            this.statusLabel.string = "测试创建回合开始节点...";
        }
        // 直接创建一个测试节点
        var testNode = new cc.Node('test_round_start');
        var canvas = cc.find('Canvas');
        if (canvas) {
            canvas.addChild(testNode);
            // 设置基本属性
            testNode.setPosition(-cc.winSize.width, 0);
            testNode.zIndex = 2000; // 设置很高的层级确保可见
            // 添加一个简单的彩色矩形
            var graphics = testNode.addComponent(cc.Graphics);
            graphics.fillColor = cc.Color.RED;
            graphics.rect(-100, -25, 200, 50);
            graphics.fill();
            // 添加文字
            var label = testNode.addComponent(cc.Label);
            label.string = "回合开始";
            label.fontSize = 24;
            // 显示节点并执行动画
            testNode.active = true;
            console.log("🧪 测试节点创建完成，开始动画");
            // 执行动画
            cc.tween(testNode)
                .to(0.5, { x: 0 }, { easing: 'quartOut' })
                .delay(1.0)
                .to(0.5, { x: cc.winSize.width }, { easing: 'quartIn' })
                .call(function () {
                testNode.removeFromParent();
                console.log("🧪 测试动画完成");
            })
                .start();
            if (this.statusLabel) {
                this.statusLabel.string = "测试节点动画已开始";
            }
        }
        else {
            if (this.statusLabel) {
                this.statusLabel.string = "找不到Canvas节点";
            }
        }
    };
    NoticeRoundStartTest.prototype.onDestroy = function () {
        if (this.testButton) {
            this.testButton.node.off('click', this.sendTestMessage, this);
        }
        if (this.firstChoiceTestButton) {
            this.firstChoiceTestButton.node.off('click', this.testFirstChoiceBonusFlow, this);
        }
        if (this.testGameStartAnimationBtn) {
            this.testGameStartAnimationBtn.node.off('click', this.testGameStartAnimation, this);
        }
        if (this.testRoundStartAnimationBtn) {
            this.testRoundStartAnimationBtn.node.off('click', this.testRoundStartAnimation, this);
        }
        if (this.testActionDisplayFlowBtn) {
            this.testActionDisplayFlowBtn.node.off('click', this.testActionDisplayWithRoundStart, this);
        }
        if (this.testCreateNodeBtn) {
            this.testCreateNodeBtn.node.off('click', this.testCreateRoundStartNode, this);
        }
    };
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "testButton", void 0);
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "firstChoiceTestButton", void 0);
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "testGameStartAnimationBtn", void 0);
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "testRoundStartAnimationBtn", void 0);
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "testActionDisplayFlowBtn", void 0);
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "testCreateNodeBtn", void 0);
    __decorate([
        property(cc.Label)
    ], NoticeRoundStartTest.prototype, "statusLabel", void 0);
    NoticeRoundStartTest = __decorate([
        ccclass
    ], NoticeRoundStartTest);
    return NoticeRoundStartTest;
}(cc.Component));
exports.default = NoticeRoundStartTest;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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